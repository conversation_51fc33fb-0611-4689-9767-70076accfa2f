import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class StudentInfo extends StatefulWidget {
  const StudentInfo({Key? key}) : super(key: key);

  @override
  _StudentInfoState createState() => _StudentInfoState();
}

class _StudentInfoState extends State<StudentInfo> {
  Map<String, dynamic>? student;
  bool isLoading = true;
  bool isError = false;
  String errorMessage = '';
  final storage = FlutterSecureStorage();

  @override
  void initState() {
    super.initState();
    fetchStudentDetails();
  }

  /// Fetch JWT token from secure storage
  Future<String?> getToken() async {
    try {
      String? token = await storage.read(key: 'jwt_token');
      return token;
    } catch (e) {
      print('DEBUG: Error reading token: $e');
      return null;
    }
  }

  /// Store JWT token in secure storage
  Future<void> storeToken(String token) async {
    await storage.write(key: 'jwt_token', value: token);
    print('DEBUG: Token stored successfully');
  }

  /// Delete JWT token from secure storage (optional for logout)
  Future<void> deleteToken() async {
    await storage.delete(key: 'jwt_token');
    print('DEBUG: Token deleted');
  }

  /// Fetch student details from API
  Future<void> fetchStudentDetails() async {
    setState(() {
      isLoading = true;
      isError = false;
    });

    try {
      final token = await getToken();
      if (token == null) {
        setState(() {
          isError = true;
          errorMessage = 'Authentication token not found. Please log in.';
          isLoading = false;
        });
        return;
      }

      final response = await http.get(
        Uri.parse('https://sasthra.in/student-dashboard'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          student = data['student'];
          isLoading = false;
        });
      } else {
        setState(() {
          isError = true;
          errorMessage = jsonDecode(response.body)['message'] ?? 'Unable to fetch student details.';
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        isError = true;
        errorMessage = 'Unable to fetch student details. Please try again.';
        isLoading = false;
      });
      print('DEBUG: Exception caught: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF10E7DC)),
              strokeWidth: 2,
            ),
            SizedBox(height: 16),
            Text(
              'Loading student information...',
              style: TextStyle(color: Colors.grey[600], fontSize: 16),
            ),
          ],
        ),
      );
    }

    if (isError) {
      return Center(
        child: Container(
          margin: EdgeInsets.all(24),
          padding: EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Color(0xFFFEE6E6),
            border: Border.all(color: Color(0xFFFECACA)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.warning, color: Colors.red[500], size: 48),
              SizedBox(height: 16),
              Text(
                'Error Loading Student Information',
                style: TextStyle(
                  color: Colors.red[800],
                  fontWeight: FontWeight.w600,
                  fontSize: 18,
                ),
              ),
              SizedBox(height: 8),
              Text(
                errorMessage,
                style: TextStyle(color: Colors.red[600], fontSize: 14),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    if (student == null) {
      return Center(
        child: Container(
          margin: EdgeInsets.all(24),
          padding: EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: Color(0xFFF7FAFC),
            border: Border.all(color: Color(0xFFE2E8F0)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.person, color: Colors.grey[400], size: 48),
              SizedBox(height: 16),
              Text(
                'No student information available',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Main student profile display
    return SingleChildScrollView(
      padding: EdgeInsets.all(24),
      child: Column(
        children: [
          CircleAvatar(
            radius: 40,
            child: Text('🎓', style: TextStyle(fontSize: 32)),
          ),
          SizedBox(height: 16),
          Text(
            '${student!['first_name']} ${student!['last_name']}',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(student!['student_email'] ?? 'N/A', style: TextStyle(fontSize: 16)),
          SizedBox(height: 24),
          Card(
            elevation: 4,
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildInfoRow('Student ID', student!['id']?.toString() ?? 'N/A'),
                  _buildInfoRow('Phone', student!['phone'] ?? 'N/A'),
                  _buildInfoRow('DOB', student!['dob'] != null
                      ? DateTime.parse(student!['dob']).toString().split(' ')[0]
                      : 'N/A'),
                  _buildInfoRow('Religion', student!['religion'] ?? 'N/A'),
                  _buildInfoRow('10th Marks', student!['marks_10th']?.toString() ?? 'N/A'),
                  _buildInfoRow('12th Marks', student!['marks_12th']?.toString() ?? 'N/A'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Text('$label: ', style: TextStyle(fontWeight: FontWeight.bold)),
          Flexible(child: Text(value)),
        ],
      ),
    );
  }
}
