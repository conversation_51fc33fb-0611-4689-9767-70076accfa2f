import 'package:flutter/material.dart';
import '../../core/services/token_service.dart';
import '../../core/services/api_service.dart';
import '../../core/utils/logger.dart';

class StudentInfo extends StatefulWidget {
  const StudentInfo({Key? key}) : super(key: key);

  @override
  _StudentInfoState createState() => _StudentInfoState();
}

class _StudentInfoState extends State<StudentInfo> {
  Map<String, dynamic>? student;
  bool isLoading = true;
  bool isError = false;
  String errorMessage = '';

  // Use global token service
  final TokenService _tokenService = TokenService();
  final ApiService _apiService = ApiService();

  @override
  void initState() {
    super.initState();
    fetchStudentDetails();
  }

  /// Refresh student data
  Future<void> refreshStudentData() async {
    await fetchStudentDetails();
  }

  /// Validate session and refresh token if needed
  Future<bool> validateAndRefreshSession() async {
    try {
      AppLogger.info('Validating session and refreshing token...');

      // Use API service to validate session and refresh token
      final data = await _apiService.validateSessionAndRefreshToken();
      final newToken = data['token'] ?? data['access_token'] ?? data['jwt_token'];

      if (newToken != null) {
        // Store the new token using global token service
        await _tokenService.storeToken(newToken);
        AppLogger.info('Session validated and token refreshed successfully');
        return true;
      } else {
        AppLogger.warning('No token received from validate-session endpoint');
        return false;
      }
    } catch (e) {
      AppLogger.error('Error validating session: $e');
      return false;
    }
  }

  /// Fetch student details from API using global services
  Future<void> fetchStudentDetails() async {
    setState(() {
      isLoading = true;
      isError = false;
    });

    try {
      // Check if token is valid first
      final isValid = await _tokenService.isTokenValid();
      if (!isValid) {
        setState(() {
          isError = true;
          errorMessage = 'Authentication token has expired. Please log in again.';
          isLoading = false;
        });
        AppLogger.warning('Authentication token is invalid or expired');
        return;
      }

      // Use API service to fetch student dashboard data
      final data = await _apiService.fetchStudentDashboard();
      AppLogger.info('Successfully fetched student dashboard data');

      setState(() {
        student = data['student'] ?? data; // Handle different response structures
        isLoading = false;
      });

    } catch (e) {
      String errorMessage = 'Unable to fetch student details. Please try again.';

      // Handle specific error types
      if (e.toString().contains('401') || e.toString().contains('Unauthorized')) {
        errorMessage = 'Session expired. Please log in again.';
      } else if (e.toString().contains('Network') || e.toString().contains('connection')) {
        errorMessage = 'Network error. Please check your connection and try again.';
      } else if (e.toString().contains('timeout')) {
        errorMessage = 'Request timeout. Please try again.';
      }

      setState(() {
        isError = true;
        this.errorMessage = errorMessage;
        isLoading = false;
      });
      AppLogger.error('Exception while fetching student dashboard: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF10E7DC)),
              strokeWidth: 2,
            ),
            SizedBox(height: 16),
            Text(
              'Loading student information...',
              style: TextStyle(color: Colors.grey[600], fontSize: 16),
            ),
          ],
        ),
      );
    }

    if (isError) {
      return Center(
        child: Container(
          margin: EdgeInsets.all(24),
          padding: EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Color(0xFFFEE6E6),
            border: Border.all(color: Color(0xFFFECACA)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.warning, color: Colors.red[500], size: 48),
              SizedBox(height: 16),
              Text(
                'Error Loading Student Information',
                style: TextStyle(
                  color: Colors.red[800],
                  fontWeight: FontWeight.w600,
                  fontSize: 18,
                ),
              ),
              SizedBox(height: 8),
              Text(
                errorMessage,
                style: TextStyle(color: Colors.red[600], fontSize: 14),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton.icon(
                    onPressed: refreshStudentData,
                    icon: const Icon(Icons.refresh),
                    label: const Text('Retry'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF10E7DC),
                      foregroundColor: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 12),
                  OutlinedButton.icon(
                    onPressed: () async {
                      final success = await validateAndRefreshSession();
                      if (success) {
                        refreshStudentData();
                      } else {
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Failed to refresh session. Please log in again.'),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      }
                    },
                    icon: const Icon(Icons.key),
                    label: const Text('Refresh Session'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: const Color(0xFF10E7DC),
                      side: const BorderSide(color: Color(0xFF10E7DC)),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    }

    if (student == null) {
      return Center(
        child: Container(
          margin: EdgeInsets.all(24),
          padding: EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: Color(0xFFF7FAFC),
            border: Border.all(color: Color(0xFFE2E8F0)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.person, color: Colors.grey[400], size: 48),
              SizedBox(height: 16),
              Text(
                'No student information available',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Main student profile display with pull-to-refresh
    return RefreshIndicator(
      onRefresh: refreshStudentData,
      color: const Color(0xFF10E7DC),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
          CircleAvatar(
            radius: 40,
            child: Text('🎓', style: TextStyle(fontSize: 32)),
          ),
          SizedBox(height: 16),
          Text(
            '${student!['first_name']} ${student!['last_name']}',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(student!['student_email'] ?? 'N/A', style: TextStyle(fontSize: 16)),
          SizedBox(height: 24),
          Card(
            elevation: 4,
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildInfoRow('Student ID', student!['id']?.toString() ?? 'N/A'),
                  _buildInfoRow('Phone', student!['phone'] ?? 'N/A'),
                  _buildInfoRow('DOB', student!['dob'] != null
                      ? DateTime.parse(student!['dob']).toString().split(' ')[0]
                      : 'N/A'),
                  _buildInfoRow('Religion', student!['religion'] ?? 'N/A'),
                  _buildInfoRow('10th Marks', student!['marks_10th']?.toString() ?? 'N/A'),
                  _buildInfoRow('12th Marks', student!['marks_12th']?.toString() ?? 'N/A'),
                ],
              ),
            ),
          ),
        ],
      ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Text('$label: ', style: TextStyle(fontWeight: FontWeight.bold)),
          Flexible(child: Text(value)),
        ],
      ),
    );
  }
}
