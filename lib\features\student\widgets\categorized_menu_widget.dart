import 'package:flutter/material.dart';
import '../../../core/services/api_service.dart';
import '../../../core/models/menu_models.dart';
import '../../../core/utils/logger.dart';

/// Widget to display categorized student menu
class CategorizedMenuWidget extends StatefulWidget {
  const CategorizedMenuWidget({Key? key}) : super(key: key);

  @override
  State<CategorizedMenuWidget> createState() => _CategorizedMenuWidgetState();
}

class _CategorizedMenuWidgetState extends State<CategorizedMenuWidget> {
  final ApiService _apiService = ApiService();
  List<MenuCategory> _categories = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadCategorizedMenu();
  }

  Future<void> _loadCategorizedMenu() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      print('🗂️ DEBUG: Loading categorized menu...');
      AppLogger.info('🗂️ MENU: Loading categorized student menu');

      final categories = await _apiService.getCategorizedStudentMenu();
      
      print('🗂️ DEBUG: Loaded ${categories.length} categories');
      for (final category in categories) {
        print('🗂️ DEBUG: Category "${category.name}" has ${category.items.length} items');
        for (final item in category.items) {
          print('🗂️ DEBUG:   - ${item.name} (${item.href})');
        }
      }

      setState(() {
        _categories = categories;
        _isLoading = false;
      });

      AppLogger.info('🗂️ MENU: Successfully loaded ${categories.length} categories');
    } catch (e) {
      print('🗂️ DEBUG: Error loading categorized menu: $e');
      AppLogger.error('🗂️ MENU: Failed to load categorized menu: $e');
      
      setState(() {
        _errorMessage = 'Failed to load menu. Please try again.';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: Color(0xFF10E7DC),
            ),
            SizedBox(height: 16),
            Text('Loading menu categories...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: TextStyle(color: Colors.red[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _loadCategorizedMenu,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF10E7DC),
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadCategorizedMenu,
      color: const Color(0xFF10E7DC),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          return _buildCategoryCard(category);
        },
      ),
    );
  }

  Widget _buildCategoryCard(MenuCategory category) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ExpansionTile(
        leading: Icon(
          _getIconData(category.icon),
          color: const Color(0xFF10E7DC),
          size: 28,
        ),
        title: Text(
          category.name,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF2D3748),
          ),
        ),
        subtitle: Text(
          '${category.items.length} items',
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 14,
          ),
        ),
        children: category.items.map((item) => _buildMenuItem(item)).toList(),
      ),
    );
  }

  Widget _buildMenuItem(MenuItem item) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
      leading: CircleAvatar(
        backgroundColor: const Color(0xFF10E7DC).withOpacity(0.1),
        child: Text(
          item.orderby.toString(),
          style: const TextStyle(
            color: Color(0xFF10E7DC),
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      title: Text(
        item.name,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        item.href,
        style: TextStyle(
          color: Colors.grey[600],
          fontSize: 12,
        ),
      ),
      trailing: const Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: Color(0xFF10E7DC),
      ),
      onTap: () {
        print('🗂️ DEBUG: Menu item tapped: ${item.name} -> ${item.href}');
        AppLogger.info('🗂️ MENU: User tapped menu item: ${item.name}');
        
        // TODO: Navigate to the appropriate screen based on item.href
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Navigating to ${item.name}'),
            backgroundColor: const Color(0xFF10E7DC),
          ),
        );
      },
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'dashboard':
        return Icons.dashboard;
      case 'live_tv':
        return Icons.live_tv;
      case 'assignment':
        return Icons.assignment;
      case 'psychology':
        return Icons.psychology;
      case 'library_books':
        return Icons.library_books;
      case 'group':
        return Icons.group;
      case 'build':
        return Icons.build;
      case 'feedback':
        return Icons.feedback;
      case 'recommend':
        return Icons.recommend;
      default:
        return Icons.menu;
    }
  }
}
