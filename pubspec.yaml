name: sasthra
description: A Flutter mobile application for Sasthra JEE NEET platform with role-based authentication and dynamic navigation.
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  workmanager: ^0.9.0+2  
  cupertino_icons: ^1.0.6
  google_fonts: ^4.0.4
  flutter_svg: ^2.0.9
  lottie: ^2.7.0
  animations: ^2.0.8
  flutter_animate: ^4.2.0+1
  flutter_automation: ^2.0.0
  lucide_icons: ^0.257.0
  webview_flutter: ^4.9.0
  provider: ^6.1.1
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9
  http: ^0.13.5
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  shared_preferences: ^2.2.2
  flutter_secure_storage: ^9.0.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  jwt_decoder: ^2.0.1
  crypto: ^3.0.3
  connectivity_plus: ^5.0.2
  device_info_plus: ^11.3.0
  package_info_plus: ^4.2.0
  permission_handler: ^11.1.0
  url_launcher: ^6.2.2
  go_router: ^12.1.3
  auto_route: ^7.9.2
  logger: ^2.0.2+1
  pretty_dio_logger: ^1.3.1
  intl: ^0.19.0
  timezone: ^0.9.2
  fl_chart: ^1.0.0
  percent_indicator: ^4.2.5
  font_awesome_flutter: ^10.9.0
  confetti: ^0.8.0
  desktop_drop: ^0.6.1
  open_filex: ^4.7.0
  file_saver: ^0.2.7
  livekit_client: ^2.1.0

dependency_overrides:
  flutter_webrtc: ^1.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  flutter_launcher_icons: ^0.14.4
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  retrofit_generator: ^8.0.4
  hive_generator: ^2.0.1
  auto_route_generator: ^7.3.2
  file_picker: ^10.2.4
  

flutter:
  uses-material-design: true

  assets:
    - assets/icons/app_icon.png
    - assets/icons/

flutter_icons:
  android: true
  ios: true
  image_path: "assets/icons/app_icon.png"
