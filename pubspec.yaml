name: sasthra
description: A Flutter mobile application for Sasthra JEE NEET platform with role-based authentication and dynamic navigation.
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  workmanager: ^0.9.0+2  

  # UI & Design
  cupertino_icons: ^1.0.6
  google_fonts: ^4.0.4
  flutter_svg: ^2.0.9
  lottie: ^2.7.0
  animations: ^2.0.8
  flutter_animate: ^4.2.0+1
  flutter_automation: ^2.0.0

  # State Management
  provider: ^6.1.1
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9

  # HTTP & API
  http: ^0.13.5
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1

  # Storage & Security
  shared_preferences: ^2.2.2
  flutter_secure_storage: ^9.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # JWT & Authentication
  jwt_decoder: ^2.0.1
  crypto: ^3.0.3

  # Utilities
  connectivity_plus: ^5.0.2
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0
  permission_handler: ^11.1.0
  url_launcher: ^6.2.2

  # Navigation
  go_router: ^12.1.3
  auto_route: ^7.9.2

  # Logging & Debugging
  logger: ^2.0.2+1
  pretty_dio_logger: ^1.3.1

  # Date & Time
  intl: ^0.19.0
  timezone: ^0.9.2
  



  # Background Tasks
  # workmanager: ^0.5.1 # Temporarily removed due to build issues

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  flutter_launcher_icons: ^0.14.4
  
  # Code Generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  retrofit_generator: ^8.0.4
  hive_generator: ^2.0.1
  auto_route_generator: ^7.3.2

flutter:
  uses-material-design: true

  assets:
    - assets/icons/app_icon.png
    - assets/icons/
    

flutter_icons:
  android: true
  ios: true
  image_path: "assets/icons/app_icon.png"