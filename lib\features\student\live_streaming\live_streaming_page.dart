import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';
import '../../../core/config/app_config.dart';

class LiveStreamingScreen extends StatefulWidget {
  const LiveStreamingScreen({super.key});

  @override
  State<LiveStreamingScreen> createState() => _LiveStreamingScreenState();
}

class _LiveStreamingScreenState extends State<LiveStreamingScreen> {
  // State flags
  bool isLoading = false;
  bool isJoining = false;
  bool isSendingMessage = false;
  bool isViewingStream = false;
  bool isChatOpen = false;
  int unreadMessages = 0;

  // Manual join field
  final TextEditingController _sessionIdController = TextEditingController();

  // Stream data
  List<Map<String, dynamic>> activeStreams = [];
  Map<String, dynamic>? currentStream;
  List<Map<String, dynamic>> chatMessages = [];
  Timer? _chatTimer;
  Timer? _streamsTimer;
  final TextEditingController _messageController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadActiveStreams();
    _startPeriodicUpdates();
  }

  @override
  void dispose() {
    _chatTimer?.cancel();
    _streamsTimer?.cancel();
    _sessionIdController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  void _startPeriodicUpdates() {
    _streamsTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _loadActiveStreams();
    });
  }

  /// Get user data from SharedPreferences
  Future<Map<String, dynamic>?> _getUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userDataString = prefs.getString(AppConfig.userDataKey);

      print('🔍 DEBUG: Getting user data from SharedPreferences');
      print('🔍 DEBUG: User data key: ${AppConfig.userDataKey}');
      print('🔍 DEBUG: Raw user data string: $userDataString');

      if (userDataString != null) {
        final userData = json.decode(userDataString);
        print('🔍 DEBUG: Parsed user data: $userData');
        return userData;
      } else {
        print('❌ DEBUG: No user data found in SharedPreferences');
        return null;
      }
    } catch (e) {
      print('❌ DEBUG: Error getting user data: $e');
      return null;
    }
  }

  Future<void> _loadActiveStreams() async {
    if (!mounted) return;
    
    setState(() {
      isLoading = true;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.get(
        Uri.parse('https://api.sasthra.com/api/live-streams/active'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (mounted) {
          setState(() {
            activeStreams = List<Map<String, dynamic>>.from(data['streams'] ?? []);
            isLoading = false;
          });
        }
      } else {
        throw Exception('Failed to load streams: ${response.statusCode}');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading streams: $e')),
        );
      }
    }
  }

  Future<void> _joinStreamBySessionId() async {
    final sessionId = _sessionIdController.text.trim();
    if (sessionId.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a session ID')),
      );
      return;
    }

    setState(() {
      isJoining = true;
    });

    try {
      print('🚀 DEBUG: Starting LiveKit join process');
      print('🚀 DEBUG: Session ID: $sessionId');

      // Get user data from SharedPreferences (optional)
      final userData = await _getUserData();

      String userId;
      String userName;

      if (userData != null) {
        // Use data from SharedPreferences if available
        userId = userData['id']?.toString() ?? 'guest_${DateTime.now().millisecondsSinceEpoch}';
        userName = userData['name']?.toString() ?? userData['username']?.toString() ?? 'Guest User';
        print('🚀 DEBUG: Using stored user data');
      } else {
        // Generate guest user data if no stored data
        userId = 'guest_${DateTime.now().millisecondsSinceEpoch}';
        userName = 'Guest User';
        print('🚀 DEBUG: No stored user data, using guest credentials');
      }

      print('🚀 DEBUG: User ID: $userId');
      print('🚀 DEBUG: User Name: $userName');

      // Get auth token from SharedPreferences using AppConfig.tokenKey
      final prefs = await SharedPreferences.getInstance();
      final authToken = prefs.getString(AppConfig.tokenKey);

      print('🚀 DEBUG: Token key from AppConfig: ${AppConfig.tokenKey}');
      print('🚀 DEBUG: Auth token exists: ${authToken != null}');
      print('🚀 DEBUG: Auth token length: ${authToken?.length ?? 0}');

      if (authToken == null || authToken.isEmpty) {
        throw Exception('Authentication token not found. Please login again.');
      }

      // Prepare API request with authentication
      final apiUrl = 'https://testing.sasthra.in/api/livekit/join';
      final requestBody = {
        'session_id': sessionId,
        'user_id': userId,
        'user_name': userName,
      };

      print('🚀 DEBUG: API URL: $apiUrl');
      print('🚀 DEBUG: Request body: $requestBody');
      print('🚀 DEBUG: Making API call with Bearer token authentication');

      // Make API call with authentication
      final response = await http.post(
        Uri.parse(apiUrl),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': 'Bearer $authToken',
        },
        body: json.encode(requestBody),
      );

      print('🚀 DEBUG: Response status code: ${response.statusCode}');
      print('🚀 DEBUG: Response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final livekitToken = responseData['livekit_token'];

        print('✅ DEBUG: LiveKit token received: ${livekitToken != null}');
        print('✅ DEBUG: LiveKit token: $livekitToken');

        if (livekitToken == null) {
          throw Exception('LiveKit token not received from server');
        }

        if (mounted) {
          setState(() {
            isJoining = false;
            isViewingStream = true;
            currentStream = {
              'id': sessionId,
              'title': 'LiveKit Session: $sessionId',
              'instructor': userName,
              'viewers': 1,
              'livekit_token': livekitToken,
              'user_id': userId,
              'user_name': userName,
            };
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Successfully joined session: $sessionId')),
          );

          print('✅ DEBUG: Successfully joined LiveKit session');
        }
      } else {
        final errorData = json.decode(response.body);
        final errorMessage = errorData['message'] ?? 'Failed to join session';
        throw Exception('API Error (${response.statusCode}): $errorMessage');
      }
    } catch (e) {
      print('❌ DEBUG: Error joining session: $e');

      if (mounted) {
        setState(() {
          isJoining = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to join session: $e')),
        );
      }
    }
  }

  void _leaveStream() {
    setState(() {
      isViewingStream = false;
      currentStream = null;
      isChatOpen = false;
      chatMessages.clear();
    });
    _chatTimer?.cancel();
  }

  Widget _buildStreamViewer() {
    return Scaffold(
      appBar: AppBar(
        title: Text(currentStream?['title'] ?? 'Live Stream'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: _leaveStream,
        ),
        actions: [
          Stack(
            children: [
              IconButton(
                icon: const Icon(Icons.chat),
                onPressed: () {
                  setState(() {
                    isChatOpen = !isChatOpen;
                    if (isChatOpen) {
                      unreadMessages = 0;
                    }
                  });
                },
              ),
              if (unreadMessages > 0)
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 16,
                      minHeight: 16,
                    ),
                    child: Text(
                      '$unreadMessages',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
      body: Row(
        children: [
          Expanded(
            flex: isChatOpen ? 3 : 1,
            child: Container(
              color: Colors.black,
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.videocam_off,
                      size: 64,
                      color: Colors.white54,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Video stream will appear here',
                      style: TextStyle(
                        color: Colors.white54,
                        fontSize: 16,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'LiveKit integration temporarily disabled',
                      style: TextStyle(
                        color: Colors.white38,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          if (isChatOpen)
            Container(
              width: 300,
              decoration: const BoxDecoration(
                border: Border(left: BorderSide(color: Colors.grey)),
              ),
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: const BoxDecoration(
                      border: Border(bottom: BorderSide(color: Colors.grey)),
                    ),
                    child: const Row(
                      children: [
                        Icon(Icons.chat),
                        SizedBox(width: 8),
                        Text(
                          'Live Chat',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Expanded(
                    child: Center(
                      child: Text(
                        'Chat functionality temporarily disabled',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: const BoxDecoration(
                      border: Border(top: BorderSide(color: Colors.grey)),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _messageController,
                            decoration: const InputDecoration(
                              hintText: 'Type a message...',
                              border: OutlineInputBorder(),
                            ),
                            enabled: false,
                          ),
                        ),
                        const SizedBox(width: 8),
                        const IconButton(
                          icon: Icon(Icons.send),
                          onPressed: null,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (isViewingStream) {
      return _buildStreamViewer();
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Live Streaming'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: RefreshIndicator(
        onRefresh: _loadActiveStreams,
        child: CustomScrollView(
          slivers: [
            // Manual Join Section
            SliverToBoxAdapter(
              child: Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Join Stream Manually',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _sessionIdController,
                            decoration: const InputDecoration(
                              labelText: 'Session ID',
                              hintText: 'Enter session ID',
                              border: OutlineInputBorder(),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        ElevatedButton(
                          onPressed: isJoining ? null : _joinStreamBySessionId,
                          child: isJoining
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                )
                              : const Text('Join'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            // Active Streams Header
            const SliverPadding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              sliver: SliverToBoxAdapter(
                child: Text(
                  'Active Streams',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ),
            ),

            // Loading or Streams List
            if (isLoading)
              const SliverFillRemaining(
                child: Center(child: CircularProgressIndicator()),
              )
            else if (activeStreams.isEmpty)
              const SliverFillRemaining(
                child: Center(
                  child: Text(
                    'No active streams available',
                    style: TextStyle(fontSize: 16, color: Colors.grey),
                  ),
                ),
              )
            else
              SliverPadding(
                padding: const EdgeInsets.all(16),
                sliver: SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final stream = activeStreams[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 12),
                        child: ListTile(
                          leading: const CircleAvatar(
                            backgroundColor: Colors.red,
                            child: Icon(Icons.live_tv, color: Colors.white),
                          ),
                          title: Text(stream['title'] ?? 'Untitled Stream'),
                          subtitle: Text(
                            'Instructor: ${stream['instructor'] ?? 'Unknown'}\n'
                            'Viewers: ${stream['viewers'] ?? 0}',
                          ),
                          trailing: const Icon(Icons.arrow_forward_ios),
                          onTap: () {
                            // Simulate joining stream
                            setState(() {
                              isViewingStream = true;
                              currentStream = stream;
                            });
                          },
                        ),
                      );
                    },
                    childCount: activeStreams.length,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
