import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/config/app_config.dart';

// Conditional import for LiveKit widget
import 'widgets/livekit_video_widget_stub.dart'
    if (dart.library.io) 'widgets/livekit_video_widget.dart';

class LiveStreamingScreen extends StatefulWidget {
  const LiveStreamingScreen({super.key});

  @override
  State<LiveStreamingScreen> createState() => _LiveStreamingScreenState();
}

class _LiveStreamingScreenState extends State<LiveStreamingScreen> {
  // State flags
  bool isLoading = false;
  bool isJoining = false;
  bool isSendingMessage = false;
  bool isViewingStream = false;
  bool isChatOpen = false;
  int unreadMessages = 0;

  // Manual join field
  final TextEditingController _sessionIdController = TextEditingController();

  // Stream data
  List<Map<String, dynamic>> activeStreams = [];
  Map<String, dynamic>? currentStream;
  List<Map<String, dynamic>> chatMessages = [];
  Timer? _chatTimer;
  Timer? _streamsTimer;
  final TextEditingController _messageController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadActiveStreams();
    _startPeriodicUpdates();
  }

  @override
  void dispose() {
    _chatTimer?.cancel();
    _streamsTimer?.cancel();
    _sessionIdController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  void _startPeriodicUpdates() {
    _streamsTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _loadActiveStreams();
    });
  }

  /// Get user data from SharedPreferences
  Future<Map<String, dynamic>?> _getUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userDataString = prefs.getString(AppConfig.userDataKey);

      print('🔍 DEBUG: Getting user data from SharedPreferences');
      print('🔍 DEBUG: User data key: ${AppConfig.userDataKey}');
      print('🔍 DEBUG: Raw user data string: $userDataString');

      if (userDataString != null) {
        final userData = json.decode(userDataString);
        print('🔍 DEBUG: Parsed user data: $userData');
        return userData;
      } else {
        print('❌ DEBUG: No user data found in SharedPreferences');
        return null;
      }
    } catch (e) {
      print('❌ DEBUG: Error getting user data: $e');
      return null;
    }
  }

  Future<void> _loadActiveStreams() async {
    if (!mounted) return;
    
    setState(() {
      isLoading = true;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.get(
        Uri.parse('https://api.sasthra.com/api/live-streams/active'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (mounted) {
          setState(() {
            activeStreams = List<Map<String, dynamic>>.from(data['streams'] ?? []);
            isLoading = false;
          });
        }
      } else {
        throw Exception('Failed to load streams: ${response.statusCode}');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading streams: $e')),
        );
      }
    }
  }

  Future<void> _joinStreamBySessionId() async {
    final sessionId = _sessionIdController.text.trim();
    if (sessionId.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a session ID')),
      );
      return;
    }

    setState(() {
      isJoining = true;
    });

    try {
      print('🚀 DEBUG: Starting LiveKit join process');
      print('🚀 DEBUG: Session ID: $sessionId');

      // Get user data from SharedPreferences
      final userData = await _getUserData();

      String userId;
      String userName;

      if (userData != null) {
        // Use data from SharedPreferences if available
        userId = userData['id']?.toString() ?? 'guest_${DateTime.now().millisecondsSinceEpoch}';
        userName = userData['name']?.toString() ?? userData['username']?.toString() ?? 'Guest User';
        print('🚀 DEBUG: Using stored user data');
      } else {
        // Generate guest user data if no stored data
        userId = 'guest_${DateTime.now().millisecondsSinceEpoch}';
        userName = 'Guest User';
        print('🚀 DEBUG: No stored user data, using guest credentials');
      }

      print('🚀 DEBUG: User ID: $userId');
      print('🚀 DEBUG: User Name: $userName');

      // Prepare API request WITHOUT authentication (as per requirement)
      final apiUrl = 'https://testing.sasthra.in/api/livekit/join';
      final requestBody = {
        'session_id': sessionId,
        'user_id': userId,
        'user_name': userName,
        'user_role': 'student',
      };

      print('🚀 DEBUG: API URL: $apiUrl');
      print('🚀 DEBUG: Request body: $requestBody');
      print('🚀 DEBUG: Making API call WITHOUT authentication (as endpoint does not require token)');

      // Make API call WITHOUT authentication
      final response = await http.post(
        Uri.parse(apiUrl),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode(requestBody),
      );

      print('🚀 DEBUG: Response status code: ${response.statusCode}');
      print('🚀 DEBUG: Response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);

        // Extract all response data
        final livekitToken = responseData['livekit_token'];
        final livekitUrl = responseData['livekit_url'];
        final participantId = responseData['participant_id'];
        final participantName = responseData['participant_name'];
        final isTeacher = responseData['is_teacher'] ?? false;
        final tokenExpiresIn = responseData['token_expires_in'];

        print('✅ DEBUG: LiveKit token received: ${livekitToken != null}');
        print('✅ DEBUG: LiveKit URL: $livekitUrl');
        print('✅ DEBUG: Participant ID: $participantId');
        print('✅ DEBUG: Participant Name: $participantName');
        print('✅ DEBUG: Is Teacher: $isTeacher');
        print('✅ DEBUG: Token expires in: $tokenExpiresIn seconds');

        if (livekitToken == null || livekitUrl == null) {
          throw Exception('LiveKit token or URL not received from server');
        }

        if (mounted) {
          setState(() {
            isJoining = false;
            isViewingStream = true;
            currentStream = {
              'id': sessionId,
              'title': 'Live Session: $sessionId',
              'instructor': participantName ?? userName,
              'viewers': 1,
              'livekit_token': livekitToken,
              'livekit_url': livekitUrl,
              'participant_id': participantId,
              'participant_name': participantName,
              'user_id': userId,
              'user_name': userName,
              'is_teacher': isTeacher,
              'token_expires_in': tokenExpiresIn,
              'session_id': sessionId,
            };
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Successfully joined session: $sessionId'),
              backgroundColor: Colors.green,
            ),
          );

          print('✅ DEBUG: Successfully joined LiveKit session');

          // Start chat polling for this session
          _startChatPolling();
        }
      } else {
        String errorMessage = 'Failed to join session';
        try {
          final errorData = json.decode(response.body);
          errorMessage = errorData['message'] ?? errorData['error'] ?? errorMessage;
        } catch (e) {
          errorMessage = 'Server returned status ${response.statusCode}';
        }
        throw Exception('API Error (${response.statusCode}): $errorMessage');
      }
    } catch (e) {
      print('❌ DEBUG: Error joining session: $e');

      if (mounted) {
        setState(() {
          isJoining = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to join session: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _startChatPolling() {
    _chatTimer?.cancel();
    _chatTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      // TODO: Implement chat polling when chat API is available
      // For now, this is a placeholder for future chat functionality
      if (!mounted || !isViewingStream) {
        timer.cancel();
      }
    });
  }

  void _showSessionInfo() {
    final stream = currentStream;
    if (stream == null) return;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Session Information'),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildInfoRow('Session ID', stream['session_id'] ?? 'Unknown'),
                _buildInfoRow('Participant ID', stream['participant_id'] ?? 'Unknown'),
                _buildInfoRow('Participant Name', stream['participant_name'] ?? 'Unknown'),
                _buildInfoRow('User Role', stream['is_teacher'] == true ? 'Teacher' : 'Student'),
                _buildInfoRow('LiveKit URL', stream['livekit_url'] ?? 'Not available'),
                _buildInfoRow('Token Expires In', '${stream['token_expires_in'] ?? 0} seconds'),
                _buildInfoRow('Connection Status', 'Connected'),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                _copySessionInfo(stream);
              },
              child: const Text('Copy Info'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  void _copySessionInfo(Map<String, dynamic> stream) {
    final sessionInfo = '''
Session Information:
- Session ID: ${stream['session_id'] ?? 'Unknown'}
- Participant ID: ${stream['participant_id'] ?? 'Unknown'}
- Participant Name: ${stream['participant_name'] ?? 'Unknown'}
- User Role: ${stream['is_teacher'] == true ? 'Teacher' : 'Student'}
- LiveKit URL: ${stream['livekit_url'] ?? 'Not available'}
- Token Expires In: ${stream['token_expires_in'] ?? 0} seconds
- Connection Status: Connected
''';

    Clipboard.setData(ClipboardData(text: sessionInfo));

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Session information copied to clipboard'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _leaveStream() {
    setState(() {
      isViewingStream = false;
      currentStream = null;
      isChatOpen = false;
      chatMessages.clear();
    });
    _chatTimer?.cancel();
  }

  Widget _buildStreamViewer() {
    final stream = currentStream;
    final sessionId = stream?['session_id'] ?? 'Unknown';
    final participantName = stream?['participant_name'] ?? stream?['user_name'] ?? 'Unknown';
    final isTeacher = stream?['is_teacher'] ?? false;

    return Scaffold(
      appBar: AppBar(
        title: Text(currentStream?['title'] ?? 'Live Stream'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: _leaveStream,
        ),
        actions: [
          // Session info button
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () {
              _showSessionInfo();
            },
          ),
          // Chat button
          Stack(
            children: [
              IconButton(
                icon: const Icon(Icons.chat),
                onPressed: () {
                  setState(() {
                    isChatOpen = !isChatOpen;
                    if (isChatOpen) {
                      unreadMessages = 0;
                    }
                  });
                },
              ),
              if (unreadMessages > 0)
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 16,
                      minHeight: 16,
                    ),
                    child: Text(
                      '$unreadMessages',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
      body: Row(
        children: [
          Expanded(
            flex: isChatOpen ? 3 : 1,
            child: Stack(
              children: [
                // LiveKit Video Widget (conditionally imported)
                LiveKitVideoWidget(
                  livekitUrl: stream?['livekit_url'] ?? '',
                  livekitToken: stream?['livekit_token'] ?? '',
                  participantName: participantName,
                  isTeacher: isTeacher,
                ),
                // Connection status overlay
                Positioned(
                  top: 16,
                  left: 16,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.9),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          kIsWeb ? 'Connected' : 'Live',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // Session info overlay
                Positioned(
                  top: 16,
                  right: 16,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.7),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      'Session: ${sessionId.length > 8 ? '${sessionId.substring(0, 8)}...' : sessionId}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          if (isChatOpen)
            Container(
              width: 300,
              decoration: const BoxDecoration(
                border: Border(left: BorderSide(color: Colors.grey)),
              ),
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: const BoxDecoration(
                      border: Border(bottom: BorderSide(color: Colors.grey)),
                    ),
                    child: const Row(
                      children: [
                        Icon(Icons.chat),
                        SizedBox(width: 8),
                        Text(
                          'Live Chat',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Expanded(
                    child: Center(
                      child: Text(
                        'Chat functionality temporarily disabled',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: const BoxDecoration(
                      border: Border(top: BorderSide(color: Colors.grey)),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _messageController,
                            decoration: const InputDecoration(
                              hintText: 'Type a message...',
                              border: OutlineInputBorder(),
                            ),
                            enabled: false,
                          ),
                        ),
                        const SizedBox(width: 8),
                        const IconButton(
                          icon: Icon(Icons.send),
                          onPressed: null,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (isViewingStream) {
      return _buildStreamViewer();
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Live Streaming'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: RefreshIndicator(
        onRefresh: _loadActiveStreams,
        child: CustomScrollView(
          slivers: [
            // Manual Join Section
            SliverToBoxAdapter(
              child: Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Join Stream Manually',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _sessionIdController,
                            decoration: const InputDecoration(
                              labelText: 'Session ID',
                              hintText: 'Enter session ID',
                              border: OutlineInputBorder(),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        ElevatedButton(
                          onPressed: isJoining ? null : _joinStreamBySessionId,
                          child: isJoining
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                )
                              : const Text('Join'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            // Active Streams Header
            const SliverPadding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              sliver: SliverToBoxAdapter(
                child: Text(
                  'Active Streams',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ),
            ),

            // Loading or Streams List
            if (isLoading)
              const SliverFillRemaining(
                child: Center(child: CircularProgressIndicator()),
              )
            else if (activeStreams.isEmpty)
              const SliverFillRemaining(
                child: Center(
                  child: Text(
                    'No active streams available',
                    style: TextStyle(fontSize: 16, color: Colors.grey),
                  ),
                ),
              )
            else
              SliverPadding(
                padding: const EdgeInsets.all(16),
                sliver: SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final stream = activeStreams[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 12),
                        child: ListTile(
                          leading: const CircleAvatar(
                            backgroundColor: Colors.red,
                            child: Icon(Icons.live_tv, color: Colors.white),
                          ),
                          title: Text(stream['title'] ?? 'Untitled Stream'),
                          subtitle: Text(
                            'Instructor: ${stream['instructor'] ?? 'Unknown'}\n'
                            'Viewers: ${stream['viewers'] ?? 0}',
                          ),
                          trailing: const Icon(Icons.arrow_forward_ios),
                          onTap: () {
                            // Simulate joining stream
                            setState(() {
                              isViewingStream = true;
                              currentStream = stream;
                            });
                          },
                        ),
                      );
                    },
                    childCount: activeStreams.length,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
