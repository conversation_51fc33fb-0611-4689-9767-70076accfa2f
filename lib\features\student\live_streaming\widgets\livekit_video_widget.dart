import 'package:flutter/material.dart';
import 'package:livekit_client/livekit_client.dart';

class LiveKitVideoWidget extends StatefulWidget {
  final String livekitUrl;
  final String livekitToken;
  final String participantName;
  final bool isTeacher;

  const LiveKitVideoWidget({
    super.key,
    required this.livekitUrl,
    required this.livekitToken,
    required this.participantName,
    required this.isTeacher,
  });

  @override
  State<LiveKitVideoWidget> createState() => _LiveKitVideoWidgetState();
}

class _LiveKitVideoWidgetState extends State<LiveKitVideoWidget> {
  Room? _room;
  bool _isConnecting = true;
  bool _isConnected = false;
  String? _errorMessage;
  List<RemoteParticipant> _remoteParticipants = [];
  LocalParticipant? _localParticipant;

  @override
  void initState() {
    super.initState();
    _connectToRoom();
  }

  @override
  void dispose() {
    _room?.disconnect();
    _room?.dispose();
    super.dispose();
  }

  Future<void> _connectToRoom() async {
    try {
      setState(() {
        _isConnecting = true;
        _errorMessage = null;
      });

      // Create room instance
      _room = Room();

      // Set up event listeners
      _room!.addListener(_onRoomUpdate);

      // Connect to the room
      await _room!.connect(
        widget.livekitUrl,
        widget.livekitToken,
        roomOptions: const RoomOptions(
          adaptiveStream: true,
          dynacast: true,
          defaultCameraCaptureOptions: CameraCaptureOptions(
            maxFrameRate: 30,
            params: VideoParametersPresets.h720_169,
          ),
          defaultScreenShareCaptureOptions: ScreenShareCaptureOptions(
            useiOSBroadcastExtension: true,
            params: VideoParametersPresets.screenShareH1080FPS15,
          ),
        ),
        fastConnectOptions: FastConnectOptions(
          microphone: TrackOption(enabled: !widget.isTeacher),
          camera: TrackOption(enabled: !widget.isTeacher),
        ),
      );

      print('✅ Connected to LiveKit room successfully');
      
      setState(() {
        _isConnecting = false;
        _isConnected = true;
        _localParticipant = _room!.localParticipant;
        _remoteParticipants = _room!.remoteParticipants.values.toList();
      });

    } catch (error) {
      print('❌ Failed to connect to LiveKit room: $error');
      setState(() {
        _isConnecting = false;
        _isConnected = false;
        _errorMessage = 'Failed to connect: $error';
      });
    }
  }

  void _onRoomUpdate() {
    if (!mounted) return;
    
    setState(() {
      _remoteParticipants = _room!.remoteParticipants.values.toList();
      _localParticipant = _room!.localParticipant;
    });
  }

  Widget _buildParticipantVideo(Participant participant) {
    final videoTrack = participant.videoTrackPublications
        .where((pub) => pub.subscribed && pub.track != null)
        .map((pub) => pub.track as VideoTrack)
        .firstOrNull;

    if (videoTrack == null) {
      return Container(
        color: Colors.grey[900],
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: Colors.blue,
                child: Text(
                  participant.name?.isNotEmpty == true 
                      ? participant.name![0].toUpperCase()
                      : '?',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                participant.name ?? 'Unknown',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                ),
              ),
              if (participant is RemoteParticipant)
                const Text(
                  'Camera off',
                  style: TextStyle(
                    color: Colors.white54,
                    fontSize: 12,
                  ),
                ),
            ],
          ),
        ),
      );
    }

    return VideoTrackRenderer(
      videoTrack,
    );
  }

  Widget _buildConnectionStatus() {
    if (_isConnecting) {
      return Container(
        color: Colors.black,
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(color: Colors.blue),
              SizedBox(height: 16),
              Text(
                'Connecting to LiveKit...',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            ],
          ),
        ),
      );
    }

    if (_errorMessage != null) {
      return Container(
        color: Colors.black,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 64,
              ),
              const SizedBox(height: 16),
              Text(
                'Connection Failed',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _errorMessage!,
                style: const TextStyle(color: Colors.white70, fontSize: 14),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _connectToRoom,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return const SizedBox.shrink();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isConnected) {
      return _buildConnectionStatus();
    }

    // Show remote participants (teachers/other students)
    if (_remoteParticipants.isNotEmpty) {
      return GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: _remoteParticipants.length == 1 ? 1 : 2,
          childAspectRatio: 16 / 9,
        ),
        itemCount: _remoteParticipants.length,
        itemBuilder: (context, index) {
          final participant = _remoteParticipants[index];
          return Container(
            margin: const EdgeInsets.all(4),
            child: Stack(
              children: [
                _buildParticipantVideo(participant),
                Positioned(
                  bottom: 8,
                  left: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      participant.name ?? 'Unknown',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      );
    }

    // No remote participants - show waiting message
    return Container(
      color: Colors.black,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              color: Colors.white54,
              size: 64,
            ),
            SizedBox(height: 16),
            Text(
              'Waiting for other participants...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'You are connected to the session',
              style: TextStyle(
                color: Colors.white54,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
