import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/widgets/base_page.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/logger.dart';

class PrivacyPolicyPage extends StatelessWidget {
  const PrivacyPolicyPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    AppLogger.userAction('Privacy Policy page viewed');
    
    return BasePage(
      title: 'Privacy Policy',
      subtitle: 'Our commitment to your privacy',
      breadcrumbs: const ['Dashboard', 'Student', 'Privacy Policy'],
      child: _buildPrivacyPolicyContent(),
    );
  }

  Widget _buildPrivacyPolicyContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Privacy Policy',
            style: AppTheme.headingLarge.copyWith(
              color: AppTheme.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ).animate().fadeIn().slideY(begin: 0.2),
          const SizedBox(height: 16),
          _buildPolicySection(
            'Information We Collect',
            'We collect information to provide better services to our users. This includes personal information such as your name, email address, phone number, and educational details. We also collect usage data to improve our services.',
          ),
          _buildPolicySection(
            'How We Use Your Information',
            'We use the information we collect to provide, maintain, and improve our services, to develop new ones, and to protect our users. We also use this information to offer you tailored content – like giving you more relevant search results and personalized learning recommendations.',
          ),
          _buildPolicySection(
            'Information Sharing',
            'We do not share your personal information with companies, organizations, or individuals outside of our organization except in the following cases:\n\n• With your consent\n• For legal reasons\n• With our trusted educational partners',
          ),
          _buildPolicySection(
            'Information Security',
            'We work hard to protect our users from unauthorized access to or unauthorized alteration, disclosure, or destruction of information we hold. We review our information collection, storage, and processing practices, including physical security measures, to prevent unauthorized access to our systems.',
          ),
          _buildPolicySection(
            'Your Rights',
            'You have the right to access, update, or delete your personal information at any time. You can also choose to opt out of certain data collection practices. Please contact our support team for assistance with managing your data preferences.',
          ),
          _buildPolicySection(
            'Changes to This Policy',
            'Our Privacy Policy may change from time to time. We will post any privacy policy changes on this page and, if the changes are significant, we will provide a more prominent notice.',
          ),
          _buildPolicySection(
            'Contact Us',
            'If you have any questions about our Privacy Policy, please contact <NAME_EMAIL>',
          ),
          const SizedBox(height: 32),
          Text(
            'Last updated: June 2023',
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.textTertiary,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPolicySection(String title, String content) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: AppTheme.headingSmall.copyWith(
              color: AppTheme.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
              height: 1.5,
            ),
          ),
        ],
      ),
    ).animate().fadeIn(delay: const Duration(milliseconds: 100)).slideY(begin: 0.1);
  }
}